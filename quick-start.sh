#!/bin/bash

echo "🏠 Haushalts-Organizer - Quick Start"
echo "===================================="

# Check if we're on macOS and suggest Homebrew
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "macOS erkannt! Installiere PHP und Composer..."
    
    # Check if Homebrew is installed
    if ! command -v brew &> /dev/null; then
        echo "❌ Homebrew nicht gefunden!"
        echo "Bitte installieren Sie Homebrew: https://brew.sh/"
        echo "Dann führen Sie dieses Script erneut aus."
        exit 1
    fi
    
    # Install PHP and Composer if not present
    if ! command -v php &> /dev/null; then
        echo "📦 Installiere PHP..."
        brew install php
    fi
    
    if ! command -v composer &> /dev/null; then
        echo "📦 Installiere Composer..."
        brew install composer
    fi
    
    if ! command -v node &> /dev/null; then
        echo "📦 Installiere Node.js..."
        brew install node
    fi
fi

# Check if required tools are available
if ! command -v php &> /dev/null; then
    echo "❌ PHP nicht gefunden! Bitte installieren Sie PHP 8.1 oder höher."
    exit 1
fi

if ! command -v composer &> /dev/null; then
    echo "❌ Composer nicht gefunden! Bitte installieren Sie Composer."
    exit 1
fi

echo "✅ PHP und Composer gefunden!"

# Install dependencies
echo "📦 Installiere PHP Dependencies..."
composer install --no-dev --optimize-autoloader

# Install Node dependencies if available
if command -v npm &> /dev/null; then
    echo "📦 Installiere Node Dependencies..."
    npm install
    echo "🎨 Kompiliere Assets..."
    npm run build
else
    echo "⚠️  Node.js nicht gefunden - CSS wird nicht kompiliert"
fi

# Setup environment
if [ ! -f .env ]; then
    echo "⚙️  Erstelle .env Datei..."
    cp .env.example .env
    php artisan key:generate
fi

# Create database directory
mkdir -p database

# Setup database
echo "🗄️  Richte SQLite Datenbank ein..."
touch database/database.sqlite

echo "🔄 Führe Migrationen aus..."
php artisan migrate --force

echo "🌱 Lade Beispieldaten..."
php artisan db:seed --force

echo "🔗 Erstelle Storage Link..."
php artisan storage:link

echo ""
echo "🎉 Setup abgeschlossen!"
echo ""
echo "🚀 Starte den Server..."
echo "Die Anwendung wird unter http://localhost:8000 verfügbar sein"
echo ""
echo "Drücken Sie Ctrl+C zum Beenden"
echo ""

# Start server
php artisan serve
