<div>
    <!-- <PERSON> Header -->
    <div class="mb-8 flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Verträge</h1>
            <p class="mt-2 text-gray-600 dark:text-gray-400">Verwalten Sie Ihre Verträge und Kündigungsfristen</p>
        </div>
        <button wire:click="openModal" class="btn-primary">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Neuer Vertrag
        </button>
    </div>

    <!-- Filters -->
    <div class="mb-6 bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Suche</label>
                <input wire:model.live="search" type="text" placeholder="Anbieter suchen..." class="form-input">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
                <select wire:model.live="filterStatus" class="form-select">
                    <option value="all">Alle</option>
                    <option value="active">Aktiv</option>
                    <option value="cancelled">Gekündigt</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Contracts Table -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div class="overflow-x-auto">
            <table class="table">
                <thead class="table-header">
                    <tr>
                        <th class="table-header-cell">Anbieter</th>
                        <th class="table-header-cell">Beginn</th>
                        <th class="table-header-cell">Laufzeit</th>
                        <th class="table-header-cell">Ende</th>
                        <th class="table-header-cell">Kündigungsfrist</th>
                        <th class="table-header-cell">Status</th>
                        <th class="table-header-cell">Aktionen</th>
                    </tr>
                </thead>
                <tbody class="table-body">
                    <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $contracts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contract): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td class="table-cell">
                                <div class="flex items-center">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($contract->provider); ?></div>
                                        <!--[if BLOCK]><![endif]--><?php if($contract->notes): ?>
                                            <div class="text-sm text-gray-500 dark:text-gray-400"><?php echo e(Str::limit($contract->notes, 50)); ?></div>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                </div>
                            </td>
                            <td class="table-cell"><?php echo e($contract->start_date->format('d.m.Y')); ?></td>
                            <td class="table-cell"><?php echo e($contract->duration_months); ?> Monate</td>
                            <td class="table-cell">
                                <?php echo e($contract->end_date ? $contract->end_date->format('d.m.Y') : '-'); ?>

                                <!--[if BLOCK]><![endif]--><?php if($contract->is_near_cancellation): ?>
                                    <div class="text-xs text-red-600 dark:text-red-400">
                                        <?php echo e($contract->days_until_cancellation); ?> Tage bis Kündigung
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </td>
                            <td class="table-cell"><?php echo e($contract->cancellation_period_days); ?> Tage</td>
                            <td class="table-cell">
                                <!--[if BLOCK]><![endif]--><?php if($contract->is_cancelled): ?>
                                    <span class="badge-danger">Gekündigt</span>
                                <?php elseif($contract->is_near_cancellation): ?>
                                    <span class="badge-warning">Kündigung möglich</span>
                                <?php else: ?>
                                    <span class="badge-success">Aktiv</span>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </td>
                            <td class="table-cell">
                                <div class="flex items-center space-x-2">
                                    <!--[if BLOCK]><![endif]--><?php if($contract->file_path): ?>
                                        <a href="<?php echo e(route('files.download', $contract->file_path)); ?>" 
                                           target="_blank"
                                           class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                        </a>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    <button wire:click="editContract(<?php echo e($contract->id); ?>)" 
                                            class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </button>
                                    <button wire:click="toggleCancellation(<?php echo e($contract->id); ?>)" 
                                            class="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300">
                                        <!--[if BLOCK]><![endif]--><?php if($contract->is_cancelled): ?>
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                            </svg>
                                        <?php else: ?>
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </button>
                                    <button wire:click="deleteContract(<?php echo e($contract->id); ?>)" 
                                            onclick="return confirmDelete('Sind Sie sicher, dass Sie diesen Vertrag löschen möchten?')"
                                            class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="table-cell text-center text-gray-500 dark:text-gray-400">
                                Keine Verträge gefunden
                            </td>
                        </tr>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </tbody>
            </table>
        </div>
        
        <!--[if BLOCK]><![endif]--><?php if($contracts->hasPages()): ?>
            <div class="px-6 py-3 border-t border-gray-200 dark:border-gray-700">
                <?php echo e($contracts->links()); ?>

            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    <!-- Modal -->
    <!--[if BLOCK]><![endif]--><?php if($showModal): ?>
        <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" wire:click="closeModal"></div>
                
                <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <form wire:submit="saveContract">
                        <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
                                <?php echo e($editingContract ? 'Vertrag bearbeiten' : 'Neuer Vertrag'); ?>

                            </h3>
                            
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Anbieter *</label>
                                    <input wire:model="provider" type="text" class="form-input" required>
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['provider'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-red-500 text-xs"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                                
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Vertragsbeginn *</label>
                                        <input wire:model="start_date" type="date" class="form-input" required>
                                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-red-500 text-xs"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Laufzeit (Monate) *</label>
                                        <input wire:model="duration_months" type="number" min="1" max="120" class="form-input" required>
                                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['duration_months'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-red-500 text-xs"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Kündigungsfrist (Tage) *</label>
                                    <input wire:model="cancellation_period_days" type="number" min="1" max="365" class="form-input" required>
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['cancellation_period_days'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-red-500 text-xs"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                                
                                <!--[if BLOCK]><![endif]--><?php if($editingContract): ?>
                                    <div>
                                        <label class="flex items-center">
                                            <input wire:model="is_cancelled" type="checkbox" class="form-checkbox">
                                            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Gekündigt</span>
                                        </label>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Vertragsdokument</label>
                                    <input wire:model="file" type="file" accept=".pdf,.jpg,.jpeg,.png" class="form-input">
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['file'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-red-500 text-xs"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                    <!--[if BLOCK]><![endif]--><?php if($file): ?>
                                        <div class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                                            Ausgewählt: <?php echo e($file->getClientOriginalName()); ?>

                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Notizen</label>
                                    <textarea wire:model="notes" rows="3" class="form-textarea"></textarea>
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-red-500 text-xs"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                            <button type="submit" class="btn-primary sm:ml-3">
                                <?php echo e($editingContract ? 'Aktualisieren' : 'Erstellen'); ?>

                            </button>
                            <button type="button" wire:click="closeModal" class="btn-secondary">
                                Abbrechen
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>
<?php /**PATH /Users/<USER>/Documents/augment-projects/daily-update/resources/views/livewire/contract-manager.blade.php ENDPATH**/ ?>