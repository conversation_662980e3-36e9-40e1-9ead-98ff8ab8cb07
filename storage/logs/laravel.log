[2025-06-15 08:43:05] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at /var/www/html/vendor/laravel/framework/src/Illuminate/Log/LogManager.php:212)
[stacktrace]
#0 /var/www/html/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 /var/www/html/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 /var/www/html/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(317): Illuminate\\Log\\LogManager->error('The \"\" director...', Array)
#4 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Symfony\\Component\\Finder\\Exception\\DirectoryNotFoundException))
#5 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(523): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Finder\\Exception\\DirectoryNotFoundException))
#6 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(203): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(Symfony\\Component\\Finder\\Exception\\DirectoryNotFoundException))
#7 /var/www/html/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-06-15 08:43:05] laravel.ERROR: The "" directory does not exist. {"exception":"[object] (Symfony\\Component\\Finder\\Exception\\DirectoryNotFoundException(code: 0): The \"\" directory does not exist. at /var/www/html/vendor/symfony/finder/Finder.php:649)
[stacktrace]
#0 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/LoadConfiguration.php(86): Symfony\\Component\\Finder\\Finder->in('')
#1 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/LoadConfiguration.php(63): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->getConfigurationFiles(Object(Illuminate\\Foundation\\Application))
#2 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/LoadConfiguration.php(39): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFiles(Object(Illuminate\\Foundation\\Application), Object(Illuminate\\Config\\Repository))
#3 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->bootstrap(Object(Illuminate\\Foundation\\Application))
#4 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#5 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#6 /var/www/html/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 {main}
"} 
[2025-06-15 10:46:43] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php:212)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(317): Illuminate\\Log\\LogManager->error('The /Users/<USER>', Array)
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Exception))
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(523): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Exception))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(203): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(Exception))
#7 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-06-15 10:46:43] laravel.ERROR: The /Users/<USER>/Documents/augment-projects/daily-update/bootstrap/cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The /Users/<USER>/Documents/augment-projects/daily-update/bootstrap/cache directory must be present and writable. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php:178)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(131): Illuminate\\Foundation\\PackageManifest->write(Array)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(107): Illuminate\\Foundation\\PackageManifest->build()
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config('aliases')
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 {main}
"} 
[2025-06-15 10:46:45] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php:212)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(317): Illuminate\\Log\\LogManager->error('The /Users/<USER>', Array)
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Exception))
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(523): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Exception))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(203): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(Exception))
#7 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-06-15 10:46:45] laravel.ERROR: The /Users/<USER>/Documents/augment-projects/daily-update/bootstrap/cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The /Users/<USER>/Documents/augment-projects/daily-update/bootstrap/cache directory must be present and writable. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php:178)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(131): Illuminate\\Foundation\\PackageManifest->write(Array)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(107): Illuminate\\Foundation\\PackageManifest->build()
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config('aliases')
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 {main}
"} 
[2025-06-15 10:46:45] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php:212)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(317): Illuminate\\Log\\LogManager->error('The /Users/<USER>', Array)
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Exception))
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(523): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Exception))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(203): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(Exception))
#7 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-06-15 10:46:45] laravel.ERROR: The /Users/<USER>/Documents/augment-projects/daily-update/bootstrap/cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The /Users/<USER>/Documents/augment-projects/daily-update/bootstrap/cache directory must be present and writable. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php:178)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(131): Illuminate\\Foundation\\PackageManifest->write(Array)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(107): Illuminate\\Foundation\\PackageManifest->build()
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config('aliases')
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 {main}
"} 
[2025-06-15 10:46:45] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php:212)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(317): Illuminate\\Log\\LogManager->error('The /Users/<USER>', Array)
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Exception))
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(523): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Exception))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(203): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(Exception))
#7 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-06-15 10:46:45] laravel.ERROR: The /Users/<USER>/Documents/augment-projects/daily-update/bootstrap/cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The /Users/<USER>/Documents/augment-projects/daily-update/bootstrap/cache directory must be present and writable. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php:178)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(131): Illuminate\\Foundation\\PackageManifest->write(Array)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(107): Illuminate\\Foundation\\PackageManifest->build()
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config('aliases')
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 {main}
"} 
[2025-06-15 10:46:45] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php:212)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(317): Illuminate\\Log\\LogManager->error('The /Users/<USER>', Array)
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Exception))
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(523): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Exception))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(203): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(Exception))
#7 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-06-15 10:46:45] laravel.ERROR: The /Users/<USER>/Documents/augment-projects/daily-update/bootstrap/cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The /Users/<USER>/Documents/augment-projects/daily-update/bootstrap/cache directory must be present and writable. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php:178)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(131): Illuminate\\Foundation\\PackageManifest->write(Array)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(107): Illuminate\\Foundation\\PackageManifest->build()
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config('aliases')
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 {main}
"} 
[2025-06-15 10:47:15] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php:212)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(317): Illuminate\\Log\\LogManager->error('Please provide ...', Array)
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(InvalidArgumentException))
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(523): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(InvalidArgumentException))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(203): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(InvalidArgumentException))
#7 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-06-15 10:47:15] laravel.ERROR: Please provide a valid cache path. {"exception":"[object] (InvalidArgumentException(code: 0): Please provide a valid cache path. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/View/Compilers/Compiler.php:67)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/View/ViewServiceProvider.php(97): Illuminate\\View\\Compilers\\Compiler->__construct(Object(Illuminate\\Filesystem\\Filesystem), NULL, '', true, 'php')
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Container.php(908): Illuminate\\View\\ViewServiceProvider->{closure:Illuminate\\View\\ViewServiceProvider::registerBladeCompiler():96}(Object(Illuminate\\Foundation\\Application), Array)
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(986): Illuminate\\Container\\Container->resolve('blade.compiler', Array, true)
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Container.php(731): Illuminate\\Foundation\\Application->resolve('blade.compiler', Array)
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(971): Illuminate\\Container\\Container->make('blade.compiler', Array)
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Container.php(1454): Illuminate\\Foundation\\Application->make('blade.compiler')
#7 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(237): Illuminate\\Container\\Container->offsetGet('blade.compiler')
#8 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('blade.compiler')
#9 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(349): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#10 /Users/<USER>/Documents/augment-projects/daily-update/vendor/livewire/livewire/src/Mechanisms/FrontendAssets/FrontendAssets.php(30): Illuminate\\Support\\Facades\\Facade::__callStatic('directive', Array)
#11 /Users/<USER>/Documents/augment-projects/daily-update/vendor/livewire/livewire/src/LivewireServiceProvider.php(77): Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets->boot()
#12 /Users/<USER>/Documents/augment-projects/daily-update/vendor/livewire/livewire/src/LivewireServiceProvider.php(19): Livewire\\LivewireServiceProvider->bootMechanisms()
#13 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Livewire\\LivewireServiceProvider->boot()
#14 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#15 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call(Array)
#19 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Livewire\\LivewireServiceProvider))
#20 [internal function]: Illuminate\\Foundation\\Application->{closure:Illuminate\\Foundation\\Application::boot():1039}(Object(Livewire\\LivewireServiceProvider), 17)
#21 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk(Array, Object(Closure))
#22 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#23 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#24 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#25 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#26 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 {main}
"} 
[2025-06-15 10:47:52] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php:212)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(317): Illuminate\\Log\\LogManager->error('Invalid route a...', Array)
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(UnexpectedValueException))
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(523): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(UnexpectedValueException))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(203): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(UnexpectedValueException))
#7 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-06-15 10:47:52] laravel.ERROR: Invalid route action: [App\Http\Controllers\Auth\EmailVerificationPromptController]. {"exception":"[object] (UnexpectedValueException(code: 0): Invalid route action: [App\\Http\\Controllers\\Auth\\EmailVerificationPromptController]. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/RouteAction.php:92)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/RouteAction.php(47): Illuminate\\Routing\\RouteAction::makeInvokable('App\\\\Http\\\\Contro...')
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Route.php(191): Illuminate\\Routing\\RouteAction::parse('verify-email', Array)
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Route.php(172): Illuminate\\Routing\\Route->parseAction(Array)
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(669): Illuminate\\Routing\\Route->__construct(Array, 'verify-email', Array)
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(561): Illuminate\\Routing\\Router->newRoute(Array, 'verify-email', Array)
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(541): Illuminate\\Routing\\Router->createRoute(Array, 'verify-email', Array)
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(159): Illuminate\\Routing\\Router->addRoute(Array, 'verify-email', 'App\\\\Http\\\\Contro...')
#7 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(355): Illuminate\\Routing\\Router->get('verify-email', 'App\\\\Http\\\\Contro...')
#8 /Users/<USER>/Documents/augment-projects/daily-update/routes/auth.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('get', Array)
#9 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(509): Illuminate\\Routing\\RouteFileRegistrar->{closure:/Users/<USER>/Documents/augment-projects/daily-update/routes/auth.php:38}(Object(Illuminate\\Routing\\Router))
#10 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(465): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#11 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#12 /Users/<USER>/Documents/augment-projects/daily-update/routes/auth.php(38): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#13 /Users/<USER>/Documents/augment-projects/daily-update/routes/web.php(59): require('/Users/<USER>/Doc...')
#14 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/RouteFileRegistrar.php(35): require('/Users/<USER>/Doc...')
#15 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('/Users/<USER>/Doc...')
#16 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(465): Illuminate\\Routing\\Router->loadRoutes('/Users/<USER>/Doc...')
#17 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, '/Users/<USER>/Doc...')
#18 /Users/<USER>/Documents/augment-projects/daily-update/app/Providers/RouteServiceProvider.php(37): Illuminate\\Routing\\RouteRegistrar->group('/Users/<USER>/Doc...')
#19 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Providers\\RouteServiceProvider->{closure:App\\Providers\\RouteServiceProvider::boot():31}()
#20 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#21 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#23 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#24 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Support/Providers/RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#25 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Support/Providers/RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#26 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->{closure:Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider::register():39}()
#27 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#28 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#29 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#30 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#31 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Support/ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#32 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1062): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#33 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#34 [internal function]: Illuminate\\Foundation\\Application->{closure:Illuminate\\Foundation\\Application::boot():1039}(Object(App\\Providers\\RouteServiceProvider), 23)
#35 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk(Array, Object(Closure))
#36 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#37 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#38 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#39 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#40 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 {main}
"} 
[2025-06-15 10:48:56] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php:212)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(317): Illuminate\\Log\\LogManager->error('Class \"App\\\\Http...', Array)
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Error))
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(523): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Error))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(203): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(Error))
#7 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-06-15 10:48:56] laravel.ERROR: Class "App\Http\Controllers\Controller" not found {"exception":"[object] (Error(code: 0): Class \"App\\Http\\Controllers\\Controller\" not found at /Users/<USER>/Documents/augment-projects/daily-update/app/Http/Controllers/Auth/EmailVerificationPromptController.php:10)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/composer/ClassLoader.php(576): include()
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/composer/ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}('/Users/<USER>/Doc...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/RouteAction.php(91): method_exists('App\\\\Http\\\\Contro...', '__invoke')
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/RouteAction.php(47): Illuminate\\Routing\\RouteAction::makeInvokable('App\\\\Http\\\\Contro...')
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Route.php(191): Illuminate\\Routing\\RouteAction::parse('verify-email', Array)
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Route.php(172): Illuminate\\Routing\\Route->parseAction(Array)
#7 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(669): Illuminate\\Routing\\Route->__construct(Array, 'verify-email', Array)
#8 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(561): Illuminate\\Routing\\Router->newRoute(Array, 'verify-email', Array)
#9 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(541): Illuminate\\Routing\\Router->createRoute(Array, 'verify-email', Array)
#10 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(159): Illuminate\\Routing\\Router->addRoute(Array, 'verify-email', 'App\\\\Http\\\\Contro...')
#11 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(355): Illuminate\\Routing\\Router->get('verify-email', 'App\\\\Http\\\\Contro...')
#12 /Users/<USER>/Documents/augment-projects/daily-update/routes/auth.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('get', Array)
#13 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(509): Illuminate\\Routing\\RouteFileRegistrar->{closure:/Users/<USER>/Documents/augment-projects/daily-update/routes/auth.php:38}(Object(Illuminate\\Routing\\Router))
#14 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(465): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#15 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#16 /Users/<USER>/Documents/augment-projects/daily-update/routes/auth.php(38): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#17 /Users/<USER>/Documents/augment-projects/daily-update/routes/web.php(59): require('/Users/<USER>/Doc...')
#18 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/RouteFileRegistrar.php(35): require('/Users/<USER>/Doc...')
#19 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('/Users/<USER>/Doc...')
#20 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(465): Illuminate\\Routing\\Router->loadRoutes('/Users/<USER>/Doc...')
#21 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, '/Users/<USER>/Doc...')
#22 /Users/<USER>/Documents/augment-projects/daily-update/app/Providers/RouteServiceProvider.php(37): Illuminate\\Routing\\RouteRegistrar->group('/Users/<USER>/Doc...')
#23 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Providers\\RouteServiceProvider->{closure:App\\Providers\\RouteServiceProvider::boot():31}()
#24 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#25 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#27 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#28 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Support/Providers/RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#29 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Support/Providers/RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#30 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->{closure:Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider::register():39}()
#31 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#32 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#33 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#34 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#35 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Support/ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#36 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1062): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#37 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#38 [internal function]: Illuminate\\Foundation\\Application->{closure:Illuminate\\Foundation\\Application::boot():1039}(Object(App\\Providers\\RouteServiceProvider), 23)
#39 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk(Array, Object(Closure))
#40 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#41 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#42 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#43 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#44 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 {main}
"} 
