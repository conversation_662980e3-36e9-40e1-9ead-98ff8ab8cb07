[2025-06-15 08:43:05] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at /var/www/html/vendor/laravel/framework/src/Illuminate/Log/LogManager.php:212)
[stacktrace]
#0 /var/www/html/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 /var/www/html/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 /var/www/html/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(317): Illuminate\\Log\\LogManager->error('The \"\" director...', Array)
#4 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Symfony\\Component\\Finder\\Exception\\DirectoryNotFoundException))
#5 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(523): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Symfony\\Component\\Finder\\Exception\\DirectoryNotFoundException))
#6 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(203): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(Symfony\\Component\\Finder\\Exception\\DirectoryNotFoundException))
#7 /var/www/html/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-06-15 08:43:05] laravel.ERROR: The "" directory does not exist. {"exception":"[object] (Symfony\\Component\\Finder\\Exception\\DirectoryNotFoundException(code: 0): The \"\" directory does not exist. at /var/www/html/vendor/symfony/finder/Finder.php:649)
[stacktrace]
#0 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/LoadConfiguration.php(86): Symfony\\Component\\Finder\\Finder->in('')
#1 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/LoadConfiguration.php(63): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->getConfigurationFiles(Object(Illuminate\\Foundation\\Application))
#2 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/LoadConfiguration.php(39): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFiles(Object(Illuminate\\Foundation\\Application), Object(Illuminate\\Config\\Repository))
#3 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->bootstrap(Object(Illuminate\\Foundation\\Application))
#4 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#5 /var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#6 /var/www/html/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 {main}
"} 
[2025-06-15 10:46:43] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php:212)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(317): Illuminate\\Log\\LogManager->error('The /Users/<USER>', Array)
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Exception))
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(523): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Exception))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(203): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(Exception))
#7 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-06-15 10:46:43] laravel.ERROR: The /Users/<USER>/Documents/augment-projects/daily-update/bootstrap/cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The /Users/<USER>/Documents/augment-projects/daily-update/bootstrap/cache directory must be present and writable. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php:178)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(131): Illuminate\\Foundation\\PackageManifest->write(Array)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(107): Illuminate\\Foundation\\PackageManifest->build()
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config('aliases')
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 {main}
"} 
[2025-06-15 10:46:45] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php:212)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(317): Illuminate\\Log\\LogManager->error('The /Users/<USER>', Array)
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Exception))
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(523): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Exception))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(203): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(Exception))
#7 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-06-15 10:46:45] laravel.ERROR: The /Users/<USER>/Documents/augment-projects/daily-update/bootstrap/cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The /Users/<USER>/Documents/augment-projects/daily-update/bootstrap/cache directory must be present and writable. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php:178)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(131): Illuminate\\Foundation\\PackageManifest->write(Array)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(107): Illuminate\\Foundation\\PackageManifest->build()
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config('aliases')
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 {main}
"} 
[2025-06-15 10:46:45] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php:212)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(317): Illuminate\\Log\\LogManager->error('The /Users/<USER>', Array)
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Exception))
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(523): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Exception))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(203): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(Exception))
#7 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-06-15 10:46:45] laravel.ERROR: The /Users/<USER>/Documents/augment-projects/daily-update/bootstrap/cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The /Users/<USER>/Documents/augment-projects/daily-update/bootstrap/cache directory must be present and writable. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php:178)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(131): Illuminate\\Foundation\\PackageManifest->write(Array)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(107): Illuminate\\Foundation\\PackageManifest->build()
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config('aliases')
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 {main}
"} 
[2025-06-15 10:46:45] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php:212)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(317): Illuminate\\Log\\LogManager->error('The /Users/<USER>', Array)
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Exception))
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(523): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Exception))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(203): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(Exception))
#7 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-06-15 10:46:45] laravel.ERROR: The /Users/<USER>/Documents/augment-projects/daily-update/bootstrap/cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The /Users/<USER>/Documents/augment-projects/daily-update/bootstrap/cache directory must be present and writable. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php:178)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(131): Illuminate\\Foundation\\PackageManifest->write(Array)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(107): Illuminate\\Foundation\\PackageManifest->build()
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config('aliases')
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 {main}
"} 
[2025-06-15 10:46:45] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php:212)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(317): Illuminate\\Log\\LogManager->error('The /Users/<USER>', Array)
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Exception))
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(523): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Exception))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(203): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(Exception))
#7 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-06-15 10:46:45] laravel.ERROR: The /Users/<USER>/Documents/augment-projects/daily-update/bootstrap/cache directory must be present and writable. {"exception":"[object] (Exception(code: 0): The /Users/<USER>/Documents/augment-projects/daily-update/bootstrap/cache directory must be present and writable. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php:178)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(131): Illuminate\\Foundation\\PackageManifest->write(Array)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(107): Illuminate\\Foundation\\PackageManifest->build()
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config('aliases')
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 {main}
"} 
[2025-06-15 10:47:15] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php:212)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(317): Illuminate\\Log\\LogManager->error('Please provide ...', Array)
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(InvalidArgumentException))
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(523): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(InvalidArgumentException))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(203): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(InvalidArgumentException))
#7 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-06-15 10:47:15] laravel.ERROR: Please provide a valid cache path. {"exception":"[object] (InvalidArgumentException(code: 0): Please provide a valid cache path. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/View/Compilers/Compiler.php:67)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/View/ViewServiceProvider.php(97): Illuminate\\View\\Compilers\\Compiler->__construct(Object(Illuminate\\Filesystem\\Filesystem), NULL, '', true, 'php')
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Container.php(908): Illuminate\\View\\ViewServiceProvider->{closure:Illuminate\\View\\ViewServiceProvider::registerBladeCompiler():96}(Object(Illuminate\\Foundation\\Application), Array)
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(986): Illuminate\\Container\\Container->resolve('blade.compiler', Array, true)
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Container.php(731): Illuminate\\Foundation\\Application->resolve('blade.compiler', Array)
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(971): Illuminate\\Container\\Container->make('blade.compiler', Array)
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Container.php(1454): Illuminate\\Foundation\\Application->make('blade.compiler')
#7 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(237): Illuminate\\Container\\Container->offsetGet('blade.compiler')
#8 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('blade.compiler')
#9 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(349): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#10 /Users/<USER>/Documents/augment-projects/daily-update/vendor/livewire/livewire/src/Mechanisms/FrontendAssets/FrontendAssets.php(30): Illuminate\\Support\\Facades\\Facade::__callStatic('directive', Array)
#11 /Users/<USER>/Documents/augment-projects/daily-update/vendor/livewire/livewire/src/LivewireServiceProvider.php(77): Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets->boot()
#12 /Users/<USER>/Documents/augment-projects/daily-update/vendor/livewire/livewire/src/LivewireServiceProvider.php(19): Livewire\\LivewireServiceProvider->bootMechanisms()
#13 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Livewire\\LivewireServiceProvider->boot()
#14 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#15 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call(Array)
#19 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Livewire\\LivewireServiceProvider))
#20 [internal function]: Illuminate\\Foundation\\Application->{closure:Illuminate\\Foundation\\Application::boot():1039}(Object(Livewire\\LivewireServiceProvider), 17)
#21 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk(Array, Object(Closure))
#22 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#23 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#24 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#25 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#26 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 {main}
"} 
[2025-06-15 10:47:52] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php:212)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(317): Illuminate\\Log\\LogManager->error('Invalid route a...', Array)
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(UnexpectedValueException))
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(523): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(UnexpectedValueException))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(203): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(UnexpectedValueException))
#7 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-06-15 10:47:52] laravel.ERROR: Invalid route action: [App\Http\Controllers\Auth\EmailVerificationPromptController]. {"exception":"[object] (UnexpectedValueException(code: 0): Invalid route action: [App\\Http\\Controllers\\Auth\\EmailVerificationPromptController]. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/RouteAction.php:92)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/RouteAction.php(47): Illuminate\\Routing\\RouteAction::makeInvokable('App\\\\Http\\\\Contro...')
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Route.php(191): Illuminate\\Routing\\RouteAction::parse('verify-email', Array)
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Route.php(172): Illuminate\\Routing\\Route->parseAction(Array)
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(669): Illuminate\\Routing\\Route->__construct(Array, 'verify-email', Array)
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(561): Illuminate\\Routing\\Router->newRoute(Array, 'verify-email', Array)
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(541): Illuminate\\Routing\\Router->createRoute(Array, 'verify-email', Array)
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(159): Illuminate\\Routing\\Router->addRoute(Array, 'verify-email', 'App\\\\Http\\\\Contro...')
#7 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(355): Illuminate\\Routing\\Router->get('verify-email', 'App\\\\Http\\\\Contro...')
#8 /Users/<USER>/Documents/augment-projects/daily-update/routes/auth.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('get', Array)
#9 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(509): Illuminate\\Routing\\RouteFileRegistrar->{closure:/Users/<USER>/Documents/augment-projects/daily-update/routes/auth.php:38}(Object(Illuminate\\Routing\\Router))
#10 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(465): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#11 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#12 /Users/<USER>/Documents/augment-projects/daily-update/routes/auth.php(38): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#13 /Users/<USER>/Documents/augment-projects/daily-update/routes/web.php(59): require('/Users/<USER>/Doc...')
#14 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/RouteFileRegistrar.php(35): require('/Users/<USER>/Doc...')
#15 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('/Users/<USER>/Doc...')
#16 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(465): Illuminate\\Routing\\Router->loadRoutes('/Users/<USER>/Doc...')
#17 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, '/Users/<USER>/Doc...')
#18 /Users/<USER>/Documents/augment-projects/daily-update/app/Providers/RouteServiceProvider.php(37): Illuminate\\Routing\\RouteRegistrar->group('/Users/<USER>/Doc...')
#19 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Providers\\RouteServiceProvider->{closure:App\\Providers\\RouteServiceProvider::boot():31}()
#20 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#21 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#23 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#24 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Support/Providers/RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#25 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Support/Providers/RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#26 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->{closure:Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider::register():39}()
#27 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#28 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#29 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#30 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#31 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Support/ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#32 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1062): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#33 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#34 [internal function]: Illuminate\\Foundation\\Application->{closure:Illuminate\\Foundation\\Application::boot():1039}(Object(App\\Providers\\RouteServiceProvider), 23)
#35 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk(Array, Object(Closure))
#36 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#37 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#38 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#39 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#40 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 {main}
"} 
[2025-06-15 10:48:56] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php:212)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(317): Illuminate\\Log\\LogManager->error('Class \"App\\\\Http...', Array)
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Error))
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(523): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Error))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(203): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(Error))
#7 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-06-15 10:48:56] laravel.ERROR: Class "App\Http\Controllers\Controller" not found {"exception":"[object] (Error(code: 0): Class \"App\\Http\\Controllers\\Controller\" not found at /Users/<USER>/Documents/augment-projects/daily-update/app/Http/Controllers/Auth/EmailVerificationPromptController.php:10)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/composer/ClassLoader.php(576): include()
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/composer/ClassLoader.php(427): {closure:Composer\\Autoload\\ClassLoader::initializeIncludeClosure():575}('/Users/<USER>/Doc...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/RouteAction.php(91): method_exists('App\\\\Http\\\\Contro...', '__invoke')
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/RouteAction.php(47): Illuminate\\Routing\\RouteAction::makeInvokable('App\\\\Http\\\\Contro...')
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Route.php(191): Illuminate\\Routing\\RouteAction::parse('verify-email', Array)
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Route.php(172): Illuminate\\Routing\\Route->parseAction(Array)
#7 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(669): Illuminate\\Routing\\Route->__construct(Array, 'verify-email', Array)
#8 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(561): Illuminate\\Routing\\Router->newRoute(Array, 'verify-email', Array)
#9 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(541): Illuminate\\Routing\\Router->createRoute(Array, 'verify-email', Array)
#10 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(159): Illuminate\\Routing\\Router->addRoute(Array, 'verify-email', 'App\\\\Http\\\\Contro...')
#11 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(355): Illuminate\\Routing\\Router->get('verify-email', 'App\\\\Http\\\\Contro...')
#12 /Users/<USER>/Documents/augment-projects/daily-update/routes/auth.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('get', Array)
#13 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(509): Illuminate\\Routing\\RouteFileRegistrar->{closure:/Users/<USER>/Documents/augment-projects/daily-update/routes/auth.php:38}(Object(Illuminate\\Routing\\Router))
#14 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(465): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#15 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#16 /Users/<USER>/Documents/augment-projects/daily-update/routes/auth.php(38): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#17 /Users/<USER>/Documents/augment-projects/daily-update/routes/web.php(59): require('/Users/<USER>/Doc...')
#18 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/RouteFileRegistrar.php(35): require('/Users/<USER>/Doc...')
#19 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('/Users/<USER>/Doc...')
#20 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(465): Illuminate\\Routing\\Router->loadRoutes('/Users/<USER>/Doc...')
#21 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, '/Users/<USER>/Doc...')
#22 /Users/<USER>/Documents/augment-projects/daily-update/app/Providers/RouteServiceProvider.php(37): Illuminate\\Routing\\RouteRegistrar->group('/Users/<USER>/Doc...')
#23 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Providers\\RouteServiceProvider->{closure:App\\Providers\\RouteServiceProvider::boot():31}()
#24 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#25 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#27 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#28 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Support/Providers/RouteServiceProvider.php(120): Illuminate\\Container\\Container->call(Object(Closure))
#29 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Support/Providers/RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#30 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->{closure:Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider::register():39}()
#31 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#32 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#33 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#34 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#35 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Support/ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#36 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1062): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#37 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\RouteServiceProvider))
#38 [internal function]: Illuminate\\Foundation\\Application->{closure:Illuminate\\Foundation\\Application::boot():1039}(Object(App\\Providers\\RouteServiceProvider), 23)
#39 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk(Array, Object(Closure))
#40 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#41 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#42 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#43 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#44 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 {main}
"} 
[2025-06-15 10:49:18] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php:212)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(317): Illuminate\\Log\\LogManager->error('Method Illumina...', Array)
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(BadMethodCallException))
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(523): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(BadMethodCallException))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(203): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(BadMethodCallException))
#7 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-06-15 10:49:18] laravel.ERROR: Method Illuminate\Foundation\Console\ClosureCommand::hourly does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Foundation\\Console\\ClosureCommand::hourly does not exist. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Macroable/Traits/Macroable.php:112)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/routes/console.php(8): Illuminate\\Console\\Command->__call('hourly', Array)
#1 /Users/<USER>/Documents/augment-projects/daily-update/app/Console/Kernel.php(29): require('/Users/<USER>/Doc...')
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(453): App\\Console\\Kernel->commands()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#4 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 {main}
"} 
[2025-06-15 10:50:08] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php:212)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Log/LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(317): Illuminate\\Log\\LogManager->error('SQLSTATE[HY000]...', Array)
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Illuminate\\Database\\QueryException))
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(523): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Illuminate\\Database\\QueryException))
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(203): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(Illuminate\\Database\\QueryException))
#7 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-06-15 10:50:08] laravel.ERROR: SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'household_organizer' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'household_organizer' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name) at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select table_na...', Array, Object(Closure))
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run('select table_na...', Array, Object(Closure))
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Connection.php(401): Illuminate\\Database\\Connection->select('select table_na...', Array, false)
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(41): Illuminate\\Database\\Connection->selectFromWriteConnection('select table_na...')
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(165): Illuminate\\Database\\Schema\\MySqlBuilder->getTables(false)
#7 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#8 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#9 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(140): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#10 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Support/helpers.php(261): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::repositoryExists():140}(1)
#11 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(140): retry(1, Object(Closure), 0, Object(Closure))
#12 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(116): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#13 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#14 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::handle():83}()
#15 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#16 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#17 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#18 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 /Users/<USER>/Documents/augment-projects/daily-update/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 /Users/<USER>/Documents/augment-projects/daily-update/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 /Users/<USER>/Documents/augment-projects/daily-update/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 /Users/<USER>/Documents/augment-projects/daily-update/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:65)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(65): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', '', Array)
#2 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'root', '', Array)
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():179}()
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1339): call_user_func(Object(Closure))
#7 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Connection.php(528): Illuminate\\Database\\Connection->getPdo()
#8 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(false)
#9 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():414}('select table_na...', Array)
#10 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select table_na...', Array, Object(Closure))
#11 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#12 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#13 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run('select table_na...', Array, Object(Closure))
#14 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Connection.php(401): Illuminate\\Database\\Connection->select('select table_na...', Array, false)
#15 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(41): Illuminate\\Database\\Connection->selectFromWriteConnection('select table_na...')
#16 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(165): Illuminate\\Database\\Schema\\MySqlBuilder->getTables(false)
#17 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#18 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#19 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(140): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#20 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Support/helpers.php(261): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::repositoryExists():140}(1)
#21 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(140): retry(1, Object(Closure), 0, Object(Closure))
#22 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(116): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#23 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#24 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::handle():83}()
#25 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#26 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#27 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#28 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#29 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#30 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#31 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#32 /Users/<USER>/Documents/augment-projects/daily-update/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#33 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#34 /Users/<USER>/Documents/augment-projects/daily-update/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 /Users/<USER>/Documents/augment-projects/daily-update/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 /Users/<USER>/Documents/augment-projects/daily-update/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 /Users/<USER>/Documents/augment-projects/daily-update/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 {main}
"} 
[2025-06-15 10:55:29] local.ERROR: Unable to locate a class or view for component [app-layout]. {"userId":1,"exception":"[object] (InvalidArgumentException(code: 0): Unable to locate a class or view for component [app-layout]. at /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/View/Compilers/ComponentTagCompiler.php:311)
[stacktrace]
#0 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/View/Compilers/ComponentTagCompiler.php(235): Illuminate\\View\\Compilers\\ComponentTagCompiler->componentClass('app-layout')
#1 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/View/Compilers/ComponentTagCompiler.php(156): Illuminate\\View\\Compilers\\ComponentTagCompiler->componentString('app-layout', Array)
#2 [internal function]: Illuminate\\View\\Compilers\\ComponentTagCompiler->{closure:Illuminate\\View\\Compilers\\ComponentTagCompiler::compileOpeningTags():151}(Array)
#3 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/View/Compilers/ComponentTagCompiler.php(151): preg_replace_callback('/\\n            <...', Object(Closure), '<x-app-layout>\\n...')
#4 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/View/Compilers/ComponentTagCompiler.php(90): Illuminate\\View\\Compilers\\ComponentTagCompiler->compileOpeningTags('<x-app-layout>\\n...')
#5 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/View/Compilers/ComponentTagCompiler.php(76): Illuminate\\View\\Compilers\\ComponentTagCompiler->compileTags('<x-app-layout>\\n...')
#6 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/View/Compilers/BladeCompiler.php(438): Illuminate\\View\\Compilers\\ComponentTagCompiler->compile('<x-app-layout>\\n...')
#7 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/View/Compilers/BladeCompiler.php(270): Illuminate\\View\\Compilers\\BladeCompiler->compileComponentTags('<x-app-layout>\\n...')
#8 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/View/Compilers/BladeCompiler.php(184): Illuminate\\View\\Compilers\\BladeCompiler->compileString('<x-app-layout>\\n...')
#9 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(64): Illuminate\\View\\Compilers\\BladeCompiler->compile('/Users/<USER>/Doc...')
#10 /Users/<USER>/Documents/augment-projects/daily-update/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('/Users/<USER>/Doc...', Array)
#11 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/View/View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('/Users/<USER>/Doc...', Array)
#12 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/View/View.php(190): Illuminate\\View\\View->getContents()
#13 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/View/View.php(159): Illuminate\\View\\View->renderContents()
#14 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#15 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#16 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#17 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#18 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#19 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():805}(Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Auth/Middleware/EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#21 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#34 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#36 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#44 /Users/<USER>/Documents/augment-projects/daily-update/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#55 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#57 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#59 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#61 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 /Users/<USER>/Documents/augment-projects/daily-update/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 /Users/<USER>/Documents/augment-projects/daily-update/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/Users/<USER>/Doc...')
#65 {main}
"} 
