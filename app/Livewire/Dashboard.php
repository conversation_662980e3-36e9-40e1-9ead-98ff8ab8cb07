<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Contract;
use App\Models\Device;
use App\Models\Task;
use App\Models\Document;

class Dashboard extends Component
{
    public $stats = [];
    public $upcomingDeadlines = [];
    public $recentTasks = [];
    public $criticalAlerts = [];

    public function mount()
    {
        $this->loadStats();
        $this->loadUpcomingDeadlines();
        $this->loadRecentTasks();
        $this->loadCriticalAlerts();
    }

    public function loadStats()
    {
        $user = auth()->user();
        
        $this->stats = [
            'active_contracts' => $user->contracts()->where('is_cancelled', false)->count(),
            'total_devices' => $user->devices()->count(),
            'pending_tasks' => $user->tasks()->pending()->count(),
            'total_documents' => $user->documents()->count(),
        ];
    }

    public function loadUpcomingDeadlines()
    {
        $user = auth()->user();
        
        // Contracts near cancellation deadline
        $contractDeadlines = $user->contracts()
            ->where('is_cancelled', false)
            ->get()
            ->filter(function ($contract) {
                return $contract->is_near_cancellation;
            })
            ->map(function ($contract) {
                return [
                    'type' => 'contract',
                    'title' => "Vertrag: {$contract->provider}",
                    'date' => $contract->cancellation_deadline,
                    'days_left' => $contract->days_until_cancellation,
                    'urgent' => $contract->days_until_cancellation <= 7,
                ];
            });

        // Devices near warranty expiry
        $deviceDeadlines = $user->devices()
            ->get()
            ->filter(function ($device) {
                return $device->is_warranty_near_expiry;
            })
            ->map(function ($device) {
                return [
                    'type' => 'device',
                    'title' => "Garantie: {$device->name}",
                    'date' => $device->warranty_end_date,
                    'days_left' => $device->days_until_warranty_expiry,
                    'urgent' => $device->days_until_warranty_expiry <= 7,
                ];
            });

        $this->upcomingDeadlines = $contractDeadlines->concat($deviceDeadlines)
            ->sortBy('date')
            ->take(5)
            ->values()
            ->toArray();
    }

    public function loadRecentTasks()
    {
        $user = auth()->user();
        
        $this->recentTasks = $user->tasks()
            ->pending()
            ->orderBy('due_date')
            ->take(5)
            ->get()
            ->toArray();
    }

    public function loadCriticalAlerts()
    {
        $user = auth()->user();
        $alerts = [];

        // Overdue tasks
        $overdueTasks = $user->tasks()->overdue()->count();
        if ($overdueTasks > 0) {
            $alerts[] = [
                'type' => 'danger',
                'message' => "{$overdueTasks} überfällige Aufgabe(n)",
                'action' => 'Aufgaben anzeigen',
                'route' => 'tasks',
            ];
        }

        // Contracts requiring immediate attention
        $urgentContracts = $user->contracts()
            ->where('is_cancelled', false)
            ->get()
            ->filter(function ($contract) {
                return $contract->days_until_cancellation !== null && $contract->days_until_cancellation <= 7;
            })
            ->count();

        if ($urgentContracts > 0) {
            $alerts[] = [
                'type' => 'warning',
                'message' => "{$urgentContracts} Vertrag/Verträge müssen bald gekündigt werden",
                'action' => 'Verträge anzeigen',
                'route' => 'contracts',
            ];
        }

        $this->criticalAlerts = $alerts;
    }

    public function render()
    {
        return view('livewire.dashboard');
    }
}
