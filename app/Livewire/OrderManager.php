<?php

namespace App\Livewire;

use App\Models\Order;
use App\Models\Category;
use App\Models\Contact;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\WithFileUploads;

class OrderManager extends Component
{
    use WithPagination, WithFileUploads;

    public $showModal = false;
    public $editingOrder = null;
    
    // Form fields
    public $title = '';
    public $description = '';
    public $type = 'other';
    public $status = 'quote_requested';
    public $start_date = '';
    public $planned_end_date = '';
    public $estimated_cost = '';
    public $quoted_cost = '';
    public $actual_cost = '';
    public $priority = 'medium';
    public $is_emergency = false;
    public $category_id = '';
    public $contact_id = '';
    public $notes = '';
    public $completion_notes = '';
    public $rating = '';
    public $review = '';
    
    // File uploads
    public $quote_file = null;
    public $invoice_file = null;
    public $additional_files = [];

    // Filters
    public $search = '';
    public $filterStatus = 'active'; // all, active, completed, overdue
    public $filterType = 'all';
    public $filterPriority = 'all';

    protected $rules = [
        'title' => 'required|string|max:255',
        'description' => 'nullable|string',
        'type' => 'required|in:repair,renovation,maintenance,installation,service,other',
        'status' => 'required|in:quote_requested,quote_received,ordered,in_progress,completed,cancelled',
        'start_date' => 'nullable|date',
        'planned_end_date' => 'nullable|date|after_or_equal:start_date',
        'estimated_cost' => 'nullable|numeric|min:0',
        'quoted_cost' => 'nullable|numeric|min:0',
        'actual_cost' => 'nullable|numeric|min:0',
        'priority' => 'required|in:low,medium,high,urgent',
        'is_emergency' => 'boolean',
        'category_id' => 'nullable|exists:categories,id',
        'contact_id' => 'nullable|exists:contacts,id',
        'notes' => 'nullable|string',
        'completion_notes' => 'nullable|string',
        'rating' => 'nullable|integer|min:1|max:5',
        'review' => 'nullable|string',
        'quote_file' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:10240',
        'invoice_file' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:10240',
    ];

    public function openModal()
    {
        $this->resetForm();
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->resetForm();
    }

    public function resetForm()
    {
        $this->editingOrder = null;
        $this->title = '';
        $this->description = '';
        $this->type = 'other';
        $this->status = 'quote_requested';
        $this->start_date = '';
        $this->planned_end_date = '';
        $this->estimated_cost = '';
        $this->quoted_cost = '';
        $this->actual_cost = '';
        $this->priority = 'medium';
        $this->is_emergency = false;
        $this->category_id = '';
        $this->contact_id = '';
        $this->notes = '';
        $this->completion_notes = '';
        $this->rating = '';
        $this->review = '';
        $this->quote_file = null;
        $this->invoice_file = null;
        $this->additional_files = [];
        $this->resetValidation();
    }

    public function editOrder($orderId)
    {
        $order = Order::findOrFail($orderId);
        $this->editingOrder = $order;
        
        $this->title = $order->title;
        $this->description = $order->description ?? '';
        $this->type = $order->type;
        $this->status = $order->status;
        $this->start_date = $order->start_date ? $order->start_date->format('Y-m-d') : '';
        $this->planned_end_date = $order->planned_end_date ? $order->planned_end_date->format('Y-m-d') : '';
        $this->estimated_cost = $order->estimated_cost ?? '';
        $this->quoted_cost = $order->quoted_cost ?? '';
        $this->actual_cost = $order->actual_cost ?? '';
        $this->priority = $order->priority;
        $this->is_emergency = $order->is_emergency;
        $this->category_id = $order->category_id ?? '';
        $this->contact_id = $order->contact_id ?? '';
        $this->notes = $order->notes ?? '';
        $this->completion_notes = $order->completion_notes ?? '';
        $this->rating = $order->rating ?? '';
        $this->review = $order->review ?? '';
        
        $this->showModal = true;
    }

    public function saveOrder()
    {
        $this->validate();

        $data = [
            'user_id' => auth()->id(),
            'title' => $this->title,
            'description' => $this->description,
            'type' => $this->type,
            'status' => $this->status,
            'start_date' => $this->start_date ?: null,
            'planned_end_date' => $this->planned_end_date ?: null,
            'estimated_cost' => $this->estimated_cost ?: null,
            'quoted_cost' => $this->quoted_cost ?: null,
            'actual_cost' => $this->actual_cost ?: null,
            'priority' => $this->priority,
            'is_emergency' => $this->is_emergency,
            'category_id' => $this->category_id ?: null,
            'contact_id' => $this->contact_id ?: null,
            'notes' => $this->notes,
            'completion_notes' => $this->completion_notes,
            'rating' => $this->rating ?: null,
            'review' => $this->review,
        ];

        // Handle file uploads
        if ($this->quote_file) {
            $data['quote_file_path'] = $this->quote_file->store('orders/quotes', 'public');
        }

        if ($this->invoice_file) {
            $data['invoice_file_path'] = $this->invoice_file->store('orders/invoices', 'public');
        }

        if ($this->editingOrder) {
            $this->editingOrder->update($data);
            session()->flash('message', 'Auftrag erfolgreich aktualisiert.');
        } else {
            Order::create($data);
            session()->flash('message', 'Auftrag erfolgreich erstellt.');
        }

        $this->closeModal();
    }

    public function deleteOrder($orderId)
    {
        $order = Order::findOrFail($orderId);
        
        // Delete associated files
        if ($order->quote_file_path) {
            \Storage::disk('public')->delete($order->quote_file_path);
        }
        if ($order->invoice_file_path) {
            \Storage::disk('public')->delete($order->invoice_file_path);
        }
        
        $order->delete();
        session()->flash('message', 'Auftrag erfolgreich gelöscht.');
    }

    public function updateStatus($orderId, $newStatus)
    {
        $order = Order::findOrFail($orderId);
        $order->update(['status' => $newStatus]);
        
        session()->flash('message', 'Status erfolgreich aktualisiert.');
    }

    public function render()
    {
        $orders = auth()->user()->orders()
            ->with(['category', 'contact'])
            ->when($this->search, function ($query) {
                $query->where('title', 'like', '%' . $this->search . '%');
            })
            ->when($this->filterStatus === 'active', function ($query) {
                $query->active();
            })
            ->when($this->filterStatus === 'completed', function ($query) {
                $query->completed();
            })
            ->when($this->filterStatus === 'overdue', function ($query) {
                $query->overdue();
            })
            ->when($this->filterType !== 'all', function ($query) {
                $query->where('type', $this->filterType);
            })
            ->when($this->filterPriority !== 'all', function ($query) {
                $query->where('priority', $this->filterPriority);
            })
            ->orderBy('is_emergency', 'desc')
            ->orderBy('priority', 'desc')
            ->orderBy('planned_end_date')
            ->paginate(15);

        $categories = Category::all();
        $contacts = Contact::where('user_id', auth()->id())->get();

        return view('livewire.order-manager', compact('orders', 'categories', 'contacts'));
    }
}
