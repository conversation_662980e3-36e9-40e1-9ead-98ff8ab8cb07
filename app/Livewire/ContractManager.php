<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Models\Contract;
use Illuminate\Support\Facades\Storage;

class ContractManager extends Component
{
    use WithFileUploads, WithPagination;

    public $showModal = false;
    public $editingContract = null;
    
    // Form fields
    public $provider = '';
    public $start_date = '';
    public $duration_months = 12;
    public $cancellation_period_days = 30;
    public $notes = '';
    public $file = null;
    public $is_cancelled = false;

    // Filters
    public $search = '';
    public $filterStatus = 'all'; // all, active, cancelled

    protected $rules = [
        'provider' => 'required|string|max:255',
        'start_date' => 'required|date',
        'duration_months' => 'required|integer|min:1|max:120',
        'cancellation_period_days' => 'required|integer|min:1|max:365',
        'notes' => 'nullable|string',
        'file' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:10240',
    ];

    public function openModal()
    {
        $this->resetForm();
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->resetForm();
    }

    public function resetForm()
    {
        $this->editingContract = null;
        $this->provider = '';
        $this->start_date = '';
        $this->duration_months = 12;
        $this->cancellation_period_days = 30;
        $this->notes = '';
        $this->file = null;
        $this->is_cancelled = false;
        $this->resetValidation();
    }

    public function editContract($contractId)
    {
        $contract = Contract::findOrFail($contractId);
        $this->editingContract = $contract;
        
        $this->provider = $contract->provider;
        $this->start_date = $contract->start_date->format('Y-m-d');
        $this->duration_months = $contract->duration_months;
        $this->cancellation_period_days = $contract->cancellation_period_days;
        $this->notes = $contract->notes ?? '';
        $this->is_cancelled = $contract->is_cancelled;
        
        $this->showModal = true;
    }

    public function saveContract()
    {
        $this->validate();

        $data = [
            'user_id' => auth()->id(),
            'provider' => $this->provider,
            'start_date' => $this->start_date,
            'duration_months' => $this->duration_months,
            'cancellation_period_days' => $this->cancellation_period_days,
            'notes' => $this->notes,
            'is_cancelled' => $this->is_cancelled,
        ];

        if ($this->file) {
            $path = $this->file->store('contracts', 'public');
            $data['file_path'] = $path;
        }

        if ($this->editingContract) {
            $this->editingContract->update($data);
            session()->flash('message', 'Vertrag erfolgreich aktualisiert.');
        } else {
            Contract::create($data);
            session()->flash('message', 'Vertrag erfolgreich erstellt.');
        }

        $this->closeModal();
    }

    public function deleteContract($contractId)
    {
        $contract = Contract::findOrFail($contractId);
        
        if ($contract->file_path) {
            Storage::disk('public')->delete($contract->file_path);
        }
        
        $contract->delete();
        session()->flash('message', 'Vertrag erfolgreich gelöscht.');
    }

    public function toggleCancellation($contractId)
    {
        $contract = Contract::findOrFail($contractId);
        $contract->update([
            'is_cancelled' => !$contract->is_cancelled,
            'cancelled_at' => $contract->is_cancelled ? null : now(),
        ]);
        
        $status = $contract->is_cancelled ? 'gekündigt' : 'reaktiviert';
        session()->flash('message', "Vertrag erfolgreich {$status}.");
    }

    public function render()
    {
        $contracts = auth()->user()->contracts()
            ->when($this->search, function ($query) {
                $query->where('provider', 'like', '%' . $this->search . '%');
            })
            ->when($this->filterStatus === 'active', function ($query) {
                $query->where('is_cancelled', false);
            })
            ->when($this->filterStatus === 'cancelled', function ($query) {
                $query->where('is_cancelled', true);
            })
            ->orderBy('start_date', 'desc')
            ->paginate(10);

        return view('livewire.contract-manager', compact('contracts'));
    }
}
