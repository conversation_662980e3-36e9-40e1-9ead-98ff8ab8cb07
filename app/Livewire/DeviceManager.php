<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Models\Device;
use Illuminate\Support\Facades\Storage;

class DeviceManager extends Component
{
    use WithFileUploads, WithPagination;

    public $showModal = false;
    public $editingDevice = null;
    
    // Form fields
    public $name = '';
    public $brand = '';
    public $model = '';
    public $serial_number = '';
    public $purchase_date = '';
    public $warranty_months = 24;
    public $notes = '';
    public $receipt = null;

    // Filters
    public $search = '';
    public $filterWarranty = 'all'; // all, active, expired, expiring

    protected $rules = [
        'name' => 'required|string|max:255',
        'brand' => 'nullable|string|max:255',
        'model' => 'nullable|string|max:255',
        'serial_number' => 'nullable|string|max:255',
        'purchase_date' => 'required|date',
        'warranty_months' => 'required|integer|min:0|max:120',
        'notes' => 'nullable|string',
        'receipt' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:10240',
    ];

    public function openModal()
    {
        $this->resetForm();
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->resetForm();
    }

    public function resetForm()
    {
        $this->editingDevice = null;
        $this->name = '';
        $this->brand = '';
        $this->model = '';
        $this->serial_number = '';
        $this->purchase_date = '';
        $this->warranty_months = 24;
        $this->notes = '';
        $this->receipt = null;
        $this->resetValidation();
    }

    public function editDevice($deviceId)
    {
        $device = Device::findOrFail($deviceId);
        $this->editingDevice = $device;
        
        $this->name = $device->name;
        $this->brand = $device->brand ?? '';
        $this->model = $device->model ?? '';
        $this->serial_number = $device->serial_number ?? '';
        $this->purchase_date = $device->purchase_date->format('Y-m-d');
        $this->warranty_months = $device->warranty_months;
        $this->notes = $device->notes ?? '';
        
        $this->showModal = true;
    }

    public function saveDevice()
    {
        $this->validate();

        $data = [
            'user_id' => auth()->id(),
            'name' => $this->name,
            'brand' => $this->brand,
            'model' => $this->model,
            'serial_number' => $this->serial_number,
            'purchase_date' => $this->purchase_date,
            'warranty_months' => $this->warranty_months,
            'notes' => $this->notes,
        ];

        if ($this->receipt) {
            $path = $this->receipt->store('devices', 'public');
            $data['receipt_path'] = $path;
        }

        if ($this->editingDevice) {
            $this->editingDevice->update($data);
            session()->flash('message', 'Gerät erfolgreich aktualisiert.');
        } else {
            Device::create($data);
            session()->flash('message', 'Gerät erfolgreich erstellt.');
        }

        $this->closeModal();
    }

    public function deleteDevice($deviceId)
    {
        $device = Device::findOrFail($deviceId);
        
        if ($device->receipt_path) {
            Storage::disk('public')->delete($device->receipt_path);
        }
        
        $device->delete();
        session()->flash('message', 'Gerät erfolgreich gelöscht.');
    }

    public function render()
    {
        $devices = auth()->user()->devices()
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('brand', 'like', '%' . $this->search . '%')
                      ->orWhere('model', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->filterWarranty === 'active', function ($query) {
                $query->whereDate('warranty_end_date', '>', now());
            })
            ->when($this->filterWarranty === 'expired', function ($query) {
                $query->whereDate('warranty_end_date', '<=', now());
            })
            ->when($this->filterWarranty === 'expiring', function ($query) {
                $query->whereBetween('warranty_end_date', [now(), now()->addDays(30)]);
            })
            ->orderBy('purchase_date', 'desc')
            ->paginate(10);

        return view('livewire.device-manager', compact('devices'));
    }
}
