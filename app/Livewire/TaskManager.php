<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Task;
use App\Models\Category;

class TaskManager extends Component
{
    use WithPagination;

    public $showModal = false;
    public $editingTask = null;
    
    // Form fields
    public $title = '';
    public $description = '';
    public $due_date = '';
    public $priority = 'medium';
    public $category_id = '';

    // Filters
    public $search = '';
    public $filterStatus = 'pending'; // all, pending, completed, overdue
    public $filterPriority = 'all';

    protected $rules = [
        'title' => 'required|string|max:255',
        'description' => 'nullable|string',
        'due_date' => 'nullable|date',
        'priority' => 'required|in:low,medium,high',
        'category_id' => 'nullable|exists:categories,id',
    ];

    public function openModal()
    {
        $this->resetForm();
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->resetForm();
    }

    public function resetForm()
    {
        $this->editingTask = null;
        $this->title = '';
        $this->description = '';
        $this->due_date = '';
        $this->priority = 'medium';
        $this->category_id = '';
        $this->resetValidation();
    }

    public function editTask($taskId)
    {
        $task = Task::findOrFail($taskId);
        $this->editingTask = $task;
        
        $this->title = $task->title;
        $this->description = $task->description ?? '';
        $this->due_date = $task->due_date ? $task->due_date->format('Y-m-d') : '';
        $this->priority = $task->priority;
        $this->category_id = $task->category_id ?? '';
        
        $this->showModal = true;
    }

    public function saveTask()
    {
        $this->validate();

        $data = [
            'user_id' => auth()->id(),
            'title' => $this->title,
            'description' => $this->description,
            'due_date' => $this->due_date ?: null,
            'priority' => $this->priority,
            'category_id' => $this->category_id ?: null,
        ];

        if ($this->editingTask) {
            $this->editingTask->update($data);
            session()->flash('message', 'Aufgabe erfolgreich aktualisiert.');
        } else {
            Task::create($data);
            session()->flash('message', 'Aufgabe erfolgreich erstellt.');
        }

        $this->closeModal();
    }

    public function toggleComplete($taskId)
    {
        $task = Task::findOrFail($taskId);
        $task->update([
            'is_completed' => !$task->is_completed,
            'completed_at' => $task->is_completed ? null : now(),
        ]);
        
        $status = $task->is_completed ? 'abgeschlossen' : 'wieder geöffnet';
        session()->flash('message', "Aufgabe erfolgreich {$status}.");
    }

    public function deleteTask($taskId)
    {
        $task = Task::findOrFail($taskId);
        $task->delete();
        session()->flash('message', 'Aufgabe erfolgreich gelöscht.');
    }

    public function render()
    {
        $tasks = auth()->user()->tasks()
            ->with('category')
            ->when($this->search, function ($query) {
                $query->where('title', 'like', '%' . $this->search . '%');
            })
            ->when($this->filterStatus === 'pending', function ($query) {
                $query->pending();
            })
            ->when($this->filterStatus === 'completed', function ($query) {
                $query->completed();
            })
            ->when($this->filterStatus === 'overdue', function ($query) {
                $query->overdue();
            })
            ->when($this->filterPriority !== 'all', function ($query) {
                $query->where('priority', $this->filterPriority);
            })
            ->orderBy('due_date')
            ->orderBy('priority', 'desc')
            ->paginate(15);

        $categories = Category::all();

        return view('livewire.task-manager', compact('tasks', 'categories'));
    }
}
