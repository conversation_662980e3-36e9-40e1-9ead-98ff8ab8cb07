<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Response;
use App\Models\Contract;
use App\Models\Device;
use App\Models\Document;

class FileController extends Controller
{
    public function download(Request $request, $path)
    {
        // Decode the path
        $decodedPath = urldecode($path);
        
        // Check if file exists
        if (!Storage::disk('public')->exists($decodedPath)) {
            abort(404, 'File not found');
        }

        // Verify user has access to this file
        $user = auth()->user();
        $hasAccess = false;

        // Check if file belongs to user's contracts
        if ($user->contracts()->where('file_path', $decodedPath)->exists()) {
            $hasAccess = true;
        }

        // Check if file belongs to user's devices
        if ($user->devices()->where('receipt_path', $decodedPath)->exists()) {
            $hasAccess = true;
        }

        // Check if file belongs to user's documents
        if ($user->documents()->where('file_path', $decodedPath)->exists()) {
            $hasAccess = true;
        }

        if (!$hasAccess) {
            abort(403, 'Unauthorized access to file');
        }

        // Get file info
        $filePath = Storage::disk('public')->path($decodedPath);
        $fileName = basename($decodedPath);
        $mimeType = Storage::disk('public')->mimeType($decodedPath);

        // Return file response
        return Response::file($filePath, [
            'Content-Type' => $mimeType,
            'Content-Disposition' => 'inline; filename="' . $fileName . '"'
        ]);
    }
}
