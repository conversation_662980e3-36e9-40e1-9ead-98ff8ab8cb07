<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Task extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'category_id',
        'title',
        'description',
        'due_date',
        'is_completed',
        'completed_at',
        'priority',
        'reminder_sent',
    ];

    protected $casts = [
        'due_date' => 'date',
        'completed_at' => 'datetime',
        'is_completed' => 'boolean',
        'reminder_sent' => 'boolean',
    ];

    const PRIORITY_LOW = 'low';
    const PRIORITY_MEDIUM = 'medium';
    const PRIORITY_HIGH = 'high';

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function scopeCompleted($query)
    {
        return $query->where('is_completed', true);
    }

    public function scopePending($query)
    {
        return $query->where('is_completed', false);
    }

    public function scopeOverdue($query)
    {
        return $query->where('is_completed', false)
                    ->where('due_date', '<', now());
    }

    public function scopeDueToday($query)
    {
        return $query->where('is_completed', false)
                    ->whereDate('due_date', today());
    }

    public function scopeDueSoon($query)
    {
        return $query->where('is_completed', false)
                    ->whereBetween('due_date', [today(), today()->addDays(7)]);
    }

    public function getIsOverdueAttribute()
    {
        return !$this->is_completed && $this->due_date && $this->due_date->isPast();
    }

    public function getIsDueTodayAttribute()
    {
        return !$this->is_completed && $this->due_date && $this->due_date->isToday();
    }

    public function getPriorityColorAttribute()
    {
        return match($this->priority) {
            self::PRIORITY_HIGH => 'red',
            self::PRIORITY_MEDIUM => 'yellow',
            self::PRIORITY_LOW => 'green',
            default => 'gray',
        };
    }
}
