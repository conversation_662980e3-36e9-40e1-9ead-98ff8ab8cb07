<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'category_id',
        'contact_id',
        'title',
        'description',
        'type',
        'status',
        'start_date',
        'planned_end_date',
        'actual_end_date',
        'estimated_cost',
        'quoted_cost',
        'actual_cost',
        'currency',
        'priority',
        'is_emergency',
        'quote_file_path',
        'invoice_file_path',
        'additional_files',
        'notes',
        'completion_notes',
        'reminder_sent',
        'reminder_date',
        'rating',
        'review',
    ];

    protected $casts = [
        'start_date' => 'date',
        'planned_end_date' => 'date',
        'actual_end_date' => 'date',
        'estimated_cost' => 'decimal:2',
        'quoted_cost' => 'decimal:2',
        'actual_cost' => 'decimal:2',
        'is_emergency' => 'boolean',
        'reminder_sent' => 'boolean',
        'reminder_date' => 'datetime',
        'additional_files' => 'array',
        'rating' => 'integer',
    ];

    // Konstanten für Enums
    const TYPE_REPAIR = 'repair';
    const TYPE_RENOVATION = 'renovation';
    const TYPE_MAINTENANCE = 'maintenance';
    const TYPE_INSTALLATION = 'installation';
    const TYPE_SERVICE = 'service';
    const TYPE_OTHER = 'other';

    const STATUS_QUOTE_REQUESTED = 'quote_requested';
    const STATUS_QUOTE_RECEIVED = 'quote_received';
    const STATUS_ORDERED = 'ordered';
    const STATUS_IN_PROGRESS = 'in_progress';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';

    const PRIORITY_LOW = 'low';
    const PRIORITY_MEDIUM = 'medium';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_URGENT = 'urgent';

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function contact()
    {
        return $this->belongsTo(Contact::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->whereNotIn('status', [self::STATUS_COMPLETED, self::STATUS_CANCELLED]);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', self::STATUS_IN_PROGRESS);
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', '!=', self::STATUS_COMPLETED)
                    ->where('planned_end_date', '<', now());
    }

    public function scopeEmergency($query)
    {
        return $query->where('is_emergency', true);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    // Accessors
    public function getIsOverdueAttribute()
    {
        return $this->status !== self::STATUS_COMPLETED 
               && $this->planned_end_date 
               && $this->planned_end_date->isPast();
    }

    public function getStatusColorAttribute()
    {
        return match($this->status) {
            self::STATUS_QUOTE_REQUESTED => 'gray',
            self::STATUS_QUOTE_RECEIVED => 'blue',
            self::STATUS_ORDERED => 'yellow',
            self::STATUS_IN_PROGRESS => 'orange',
            self::STATUS_COMPLETED => 'green',
            self::STATUS_CANCELLED => 'red',
            default => 'gray',
        };
    }

    public function getPriorityColorAttribute()
    {
        return match($this->priority) {
            self::PRIORITY_URGENT => 'red',
            self::PRIORITY_HIGH => 'orange',
            self::PRIORITY_MEDIUM => 'yellow',
            self::PRIORITY_LOW => 'green',
            default => 'gray',
        };
    }

    public function getTypeDisplayAttribute()
    {
        return match($this->type) {
            self::TYPE_REPAIR => 'Reparatur',
            self::TYPE_RENOVATION => 'Renovierung',
            self::TYPE_MAINTENANCE => 'Wartung',
            self::TYPE_INSTALLATION => 'Installation',
            self::TYPE_SERVICE => 'Dienstleistung',
            self::TYPE_OTHER => 'Sonstiges',
            default => 'Unbekannt',
        };
    }

    public function getStatusDisplayAttribute()
    {
        return match($this->status) {
            self::STATUS_QUOTE_REQUESTED => 'Angebot angefragt',
            self::STATUS_QUOTE_RECEIVED => 'Angebot erhalten',
            self::STATUS_ORDERED => 'Beauftragt',
            self::STATUS_IN_PROGRESS => 'In Arbeit',
            self::STATUS_COMPLETED => 'Abgeschlossen',
            self::STATUS_CANCELLED => 'Storniert',
            default => 'Unbekannt',
        };
    }

    public function getPriorityDisplayAttribute()
    {
        return match($this->priority) {
            self::PRIORITY_URGENT => 'Dringend',
            self::PRIORITY_HIGH => 'Hoch',
            self::PRIORITY_MEDIUM => 'Mittel',
            self::PRIORITY_LOW => 'Niedrig',
            default => 'Unbekannt',
        };
    }

    public function getDurationInDaysAttribute()
    {
        if (!$this->start_date || !$this->actual_end_date) {
            return null;
        }
        
        return $this->start_date->diffInDays($this->actual_end_date);
    }

    public function getCostVarianceAttribute()
    {
        if (!$this->quoted_cost || !$this->actual_cost) {
            return null;
        }
        
        return $this->actual_cost - $this->quoted_cost;
    }

    public function getCostVariancePercentAttribute()
    {
        if (!$this->quoted_cost || !$this->actual_cost || $this->quoted_cost == 0) {
            return null;
        }
        
        return (($this->actual_cost - $this->quoted_cost) / $this->quoted_cost) * 100;
    }
}
