<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Contract extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'provider',
        'start_date',
        'duration_months',
        'cancellation_period_days',
        'end_date',
        'is_cancelled',
        'cancelled_at',
        'file_path',
        'notes',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'cancelled_at' => 'datetime',
        'is_cancelled' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($contract) {
            if ($contract->start_date && $contract->duration_months) {
                $contract->end_date = Carbon::parse($contract->start_date)
                    ->addMonths($contract->duration_months);
            }

            // Auto-cancel if end date has passed
            if ($contract->end_date && $contract->end_date->isPast() && !$contract->is_cancelled) {
                $contract->is_cancelled = true;
                $contract->cancelled_at = now();
            }
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function getCancellationDeadlineAttribute()
    {
        if (!$this->end_date || !$this->cancellation_period_days) {
            return null;
        }

        return $this->end_date->subDays($this->cancellation_period_days);
    }

    public function getIsNearCancellationAttribute()
    {
        $deadline = $this->cancellation_deadline;
        
        if (!$deadline || $this->is_cancelled) {
            return false;
        }

        return now()->gte($deadline) && now()->lte($this->end_date);
    }

    public function getDaysUntilCancellationAttribute()
    {
        $deadline = $this->cancellation_deadline;
        
        if (!$deadline || $this->is_cancelled) {
            return null;
        }

        return now()->diffInDays($deadline, false);
    }
}
