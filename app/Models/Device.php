<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Device extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'purchase_date',
        'warranty_months',
        'warranty_end_date',
        'receipt_path',
        'notes',
        'brand',
        'model',
        'serial_number',
    ];

    protected $casts = [
        'purchase_date' => 'date',
        'warranty_end_date' => 'date',
    ];

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($device) {
            if ($device->purchase_date && $device->warranty_months) {
                $device->warranty_end_date = Carbon::parse($device->purchase_date)
                    ->addMonths($device->warranty_months);
            }
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function getIsWarrantyExpiredAttribute()
    {
        return $this->warranty_end_date && $this->warranty_end_date->isPast();
    }

    public function getIsWarrantyNearExpiryAttribute()
    {
        if (!$this->warranty_end_date || $this->is_warranty_expired) {
            return false;
        }

        return now()->diffInDays($this->warranty_end_date) <= 30;
    }

    public function getDaysUntilWarrantyExpiryAttribute()
    {
        if (!$this->warranty_end_date || $this->is_warranty_expired) {
            return null;
        }

        return now()->diffInDays($this->warranty_end_date);
    }
}
