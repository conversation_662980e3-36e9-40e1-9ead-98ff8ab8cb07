@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
    .btn {
        @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition duration-150 ease-in-out;
    }
    
    .btn-primary {
        @apply btn text-white bg-primary-600 hover:bg-primary-700 focus:ring-primary-500;
    }
    
    .btn-secondary {
        @apply btn text-gray-700 bg-white border-gray-300 hover:bg-gray-50 focus:ring-primary-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700;
    }
    
    .btn-danger {
        @apply btn text-white bg-red-600 hover:bg-red-700 focus:ring-red-500;
    }
    
    .form-input {
        @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:border-primary-500;
    }
    
    .form-select {
        @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:border-primary-500;
    }
    
    .form-textarea {
        @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:border-primary-500;
    }
    
    .form-checkbox {
        @apply rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600;
    }
    
    .card {
        @apply bg-white dark:bg-gray-800 shadow rounded-lg;
    }
    
    .card-header {
        @apply px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700;
    }
    
    .card-body {
        @apply px-4 py-5 sm:p-6;
    }
    
    .badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    }
    
    .badge-success {
        @apply badge bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-200;
    }
    
    .badge-warning {
        @apply badge bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-200;
    }
    
    .badge-danger {
        @apply badge bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-200;
    }
    
    .badge-info {
        @apply badge bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-200;
    }
    
    .table {
        @apply min-w-full divide-y divide-gray-200 dark:divide-gray-700;
    }
    
    .table-header {
        @apply bg-gray-50 dark:bg-gray-800;
    }
    
    .table-header-cell {
        @apply px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider;
    }
    
    .table-body {
        @apply bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700;
    }
    
    .table-cell {
        @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100;
    }
}
