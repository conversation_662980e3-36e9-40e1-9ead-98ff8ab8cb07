<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\FileController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

Route::get('/', function () {
    return redirect()->route('dashboard');
});

Route::middleware(['auth', 'verified'])->group(function () {
    // Dashboard
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');

    // Contracts
    Route::get('/contracts', function () {
        return view('contracts');
    })->name('contracts.index');

    // Devices
    Route::get('/devices', function () {
        return view('devices');
    })->name('devices.index');

    // Documents
    Route::get('/documents', function () {
        return view('documents');
    })->name('documents.index');

    // Contacts
    Route::get('/contacts', function () {
        return view('contacts');
    })->name('contacts.index');

    // Tasks
    Route::get('/tasks', function () {
        return view('tasks');
    })->name('tasks.index');

    // File downloads
    Route::get('/files/{path}', [FileController::class, 'download'])
        ->where('path', '.*')
        ->name('files.download');

    // Profile
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
