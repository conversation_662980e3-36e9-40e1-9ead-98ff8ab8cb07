<?php

namespace Database\Seeders;

use App\Models\Order;
use App\Models\User;
use App\Models\Category;
use App\Models\Contact;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class OrderSeeder extends Seeder
{
    public function run(): void
    {
        $users = User::all();
        
        if ($users->isEmpty()) {
            return;
        }

        foreach ($users as $user) {
            // Get user's categories and contacts
            $categories = Category::all();
            $contacts = Contact::where('user_id', $user->id)->get();

            $orders = [
                [
                    'title' => 'Heizung reparieren',
                    'description' => 'Die Heizung im Wohnzimmer funktioniert nicht mehr. Thermostat defekt.',
                    'type' => 'repair',
                    'status' => 'in_progress',
                    'priority' => 'high',
                    'is_emergency' => false,
                    'start_date' => Carbon::now()->subDays(3),
                    'planned_end_date' => Carbon::now()->addDays(2),
                    'estimated_cost' => 250.00,
                    'quoted_cost' => 320.00,
                    'notes' => 'Handwerker kommt am Donnerstag zwischen 8-12 Uhr.',
                ],
                [
                    'title' => 'Badezimmer renovieren',
                    'description' => 'Komplette Badezimmerrenovierung: neue Fliesen, Sanitäranlagen und Beleuchtung.',
                    'type' => 'renovation',
                    'status' => 'quote_received',
                    'priority' => 'medium',
                    'is_emergency' => false,
                    'start_date' => Carbon::now()->addWeeks(2),
                    'planned_end_date' => Carbon::now()->addWeeks(6),
                    'estimated_cost' => 8000.00,
                    'quoted_cost' => 9500.00,
                    'notes' => 'Angebot von 3 verschiedenen Handwerkern eingeholt.',
                ],
                [
                    'title' => 'Waschmaschine anschließen',
                    'description' => 'Neue Waschmaschine muss angeschlossen werden. Wasseranschluss und Abfluss prüfen.',
                    'type' => 'installation',
                    'status' => 'completed',
                    'priority' => 'medium',
                    'is_emergency' => false,
                    'start_date' => Carbon::now()->subWeeks(1),
                    'planned_end_date' => Carbon::now()->subWeeks(1)->addDays(1),
                    'actual_end_date' => Carbon::now()->subWeeks(1)->addDays(1),
                    'estimated_cost' => 150.00,
                    'quoted_cost' => 180.00,
                    'actual_cost' => 180.00,
                    'rating' => 5,
                    'review' => 'Sehr professionell und pünktlich. Gerne wieder!',
                    'completion_notes' => 'Installation erfolgreich abgeschlossen. Alle Anschlüsse geprüft.',
                ],
                [
                    'title' => 'Dachrinne reinigen',
                    'description' => 'Jährliche Reinigung der Dachrinne und Überprüfung auf Schäden.',
                    'type' => 'maintenance',
                    'status' => 'quote_requested',
                    'priority' => 'low',
                    'is_emergency' => false,
                    'planned_end_date' => Carbon::now()->addMonths(1),
                    'estimated_cost' => 120.00,
                    'notes' => 'Sollte vor dem Winter erledigt werden.',
                ],
                [
                    'title' => 'Wasserrohrbruch Notfall',
                    'description' => 'Wasserrohr in der Küche geplatzt. Sofortige Reparatur erforderlich!',
                    'type' => 'repair',
                    'status' => 'completed',
                    'priority' => 'urgent',
                    'is_emergency' => true,
                    'start_date' => Carbon::now()->subDays(10),
                    'planned_end_date' => Carbon::now()->subDays(10),
                    'actual_end_date' => Carbon::now()->subDays(10),
                    'estimated_cost' => 300.00,
                    'quoted_cost' => 450.00,
                    'actual_cost' => 520.00,
                    'rating' => 4,
                    'review' => 'Schnelle Hilfe, aber etwas teurer als erwartet.',
                    'completion_notes' => 'Notfallreparatur erfolgreich. Neues Rohr installiert.',
                ],
                [
                    'title' => 'Garten neu gestalten',
                    'description' => 'Komplette Neugestaltung des Vorgartens mit neuen Pflanzen und Wegen.',
                    'type' => 'service',
                    'status' => 'ordered',
                    'priority' => 'low',
                    'is_emergency' => false,
                    'start_date' => Carbon::now()->addMonths(2),
                    'planned_end_date' => Carbon::now()->addMonths(3),
                    'estimated_cost' => 2500.00,
                    'quoted_cost' => 2800.00,
                    'notes' => 'Arbeiten beginnen im Frühjahr.',
                ],
            ];

            foreach ($orders as $orderData) {
                $orderData['user_id'] = $user->id;
                
                // Randomly assign category and contact if available
                if ($categories->isNotEmpty() && rand(0, 1)) {
                    $orderData['category_id'] = $categories->random()->id;
                }
                
                if ($contacts->isNotEmpty() && rand(0, 1)) {
                    $orderData['contact_id'] = $contacts->random()->id;
                }

                Order::create($orderData);
            }
        }
    }
}
