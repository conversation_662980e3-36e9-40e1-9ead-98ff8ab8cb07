<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    public function run(): void
    {
        $categories = [
            ['name' => 'Strom', 'color' => 'yellow', 'icon' => '⚡'],
            ['name' => 'Internet', 'color' => 'blue', 'icon' => '🌐'],
            ['name' => 'Möbel', 'color' => 'brown', 'icon' => '🪑'],
            ['name' => 'Elektronik', 'color' => 'purple', 'icon' => '📱'],
            ['name' => 'Versicherung', 'color' => 'green', 'icon' => '🛡️'],
            ['name' => 'Miete', 'color' => 'red', 'icon' => '🏠'],
            ['name' => 'Wartung', 'color' => 'orange', 'icon' => '🔧'],
            ['name' => 'Hausputz', 'color' => 'pink', 'icon' => '🧹'],
            ['name' => 'TÜV', 'color' => 'gray', 'icon' => '🚗'],
            ['name' => 'Sonstiges', 'color' => 'indigo', 'icon' => '📄'],
        ];

        foreach ($categories as $category) {
            Category::firstOrCreate(
                ['name' => $category['name']],
                $category
            );
        }
    }
}
