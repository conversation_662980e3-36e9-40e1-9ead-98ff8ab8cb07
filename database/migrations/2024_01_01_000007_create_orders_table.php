<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('category_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('contact_id')->nullable()->constrained()->onDelete('set null');
            
            // Grunddaten
            $table->string('title');
            $table->text('description')->nullable();
            $table->enum('type', ['repair', 'renovation', 'maintenance', 'installation', 'service', 'other'])->default('other');
            
            // Status und Termine
            $table->enum('status', ['quote_requested', 'quote_received', 'ordered', 'in_progress', 'completed', 'cancelled'])->default('quote_requested');
            $table->date('start_date')->nullable();
            $table->date('planned_end_date')->nullable();
            $table->date('actual_end_date')->nullable();
            
            // Kosten
            $table->decimal('estimated_cost', 10, 2)->nullable();
            $table->decimal('quoted_cost', 10, 2)->nullable();
            $table->decimal('actual_cost', 10, 2)->nullable();
            $table->string('currency', 3)->default('EUR');
            
            // Priorität und Dringlichkeit
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->boolean('is_emergency')->default(false);
            
            // Dokumente und Notizen
            $table->string('quote_file_path')->nullable();
            $table->string('invoice_file_path')->nullable();
            $table->json('additional_files')->nullable(); // Array von Dateipfaden
            $table->text('notes')->nullable();
            $table->text('completion_notes')->nullable();
            
            // Erinnerungen
            $table->boolean('reminder_sent')->default(false);
            $table->timestamp('reminder_date')->nullable();
            
            // Bewertung
            $table->integer('rating')->nullable(); // 1-5 Sterne
            $table->text('review')->nullable();
            
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
