{"name": "nunomaduro/termwind", "description": "Its like Tailwind CSS, but for the console.", "keywords": ["php", "cli", "package", "console", "css", "style"], "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "ext-mbstring": "*", "symfony/console": "^6.4.15"}, "require-dev": {"illuminate/console": "^10.48.24", "illuminate/support": "^10.48.24", "laravel/pint": "^1.18.2", "pestphp/pest": "^2.36.0", "pestphp/pest-plugin-mock": "2.0.0", "phpstan/phpstan": "^1.12.11", "phpstan/phpstan-strict-rules": "^1.6.1", "symfony/var-dumper": "^6.4.15", "thecodingmachine/phpstan-strict-rules": "^1.0.0"}, "autoload": {"psr-4": {"Termwind\\": "src/"}, "files": ["src/Functions.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "config": {"sort-packages": true, "preferred-install": "dist", "allow-plugins": {"pestphp/pest-plugin": true}}, "scripts": {"lint": "pint -v", "test:lint": "pint --test -v", "test:types": "phpstan analyse --ansi", "test:unit": "pest --colors=always", "test": ["@test:lint", "@test:types", "@test:unit"]}, "extra": {"laravel": {"providers": ["Termwind\\Laravel\\TermwindServiceProvider"]}}}