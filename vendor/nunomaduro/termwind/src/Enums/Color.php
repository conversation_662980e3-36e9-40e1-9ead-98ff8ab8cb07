<?php

declare(strict_types=1);

namespace Termwind\Enums;

final class Color
{
    public const BLACK = 'black';

    public const WHITE = 'white';

    public const BRIGHTWHITE = 'bright-white';

    public const SLATE_50 = '#f8fafc';

    public const SLATE_100 = '#f1f5f9';

    public const SLATE_200 = '#e2e8f0';

    public const SLATE_300 = '#cbd5e1';

    public const SLATE_400 = '#94a3b8';

    public const SLATE_500 = '#64748b';

    public const SLATE_600 = '#475569';

    public const SLATE_700 = '#334155';

    public const SLATE_800 = '#1e293b';

    public const SLATE_900 = '#0f172a';

    public const GRAY = 'gray';

    public const GRAY_50 = '#f9fafb';

    public const GRAY_100 = '#f3f4f6';

    public const GRAY_200 = '#e5e7eb';

    public const GRAY_300 = '#d1d5db';

    public const GRAY_400 = '#9ca3af';

    public const GRAY_500 = '#6b7280';

    public const GRAY_600 = '#4b5563';

    public const GRAY_700 = '#374151';

    public const GRAY_800 = '#1f2937';

    public const GRAY_900 = '#111827';

    public const ZINC_50 = '#fafafa';

    public const ZINC_100 = '#f4f4f5';

    public const ZINC_200 = '#e4e4e7';

    public const ZINC_300 = '#d4d4d8';

    public const ZINC_400 = '#a1a1aa';

    public const ZINC_500 = '#71717a';

    public const ZINC_600 = '#52525b';

    public const ZINC_700 = '#3f3f46';

    public const ZINC_800 = '#27272a';

    public const ZINC_900 = '#18181b';

    public const NEUTRAL_50 = '#fafafa';

    public const NEUTRAL_100 = '#f5f5f5';

    public const NEUTRAL_200 = '#e5e5e5';

    public const NEUTRAL_300 = '#d4d4d4';

    public const NEUTRAL_400 = '#a3a3a3';

    public const NEUTRAL_500 = '#737373';

    public const NEUTRAL_600 = '#525252';

    public const NEUTRAL_700 = '#404040';

    public const NEUTRAL_800 = '#262626';

    public const NEUTRAL_900 = '#171717';

    public const STONE_50 = '#fafaf9';

    public const STONE_100 = '#f5f5f4';

    public const STONE_200 = '#e7e5e4';

    public const STONE_300 = '#d6d3d1';

    public const STONE_400 = '#a8a29e';

    public const STONE_500 = '#78716c';

    public const STONE_600 = '#57534e';

    public const STONE_700 = '#44403c';

    public const STONE_800 = '#292524';

    public const STONE_900 = '#1c1917';

    public const RED = 'red';

    public const BRIGHTRED = 'bright-red';

    public const RED_50 = '#fef2f2';

    public const RED_100 = '#fee2e2';

    public const RED_200 = '#fecaca';

    public const RED_300 = '#fca5a5';

    public const RED_400 = '#f87171';

    public const RED_500 = '#ef4444';

    public const RED_600 = '#dc2626';

    public const RED_700 = '#b91c1c';

    public const RED_800 = '#991b1b';

    public const RED_900 = '#7f1d1d';

    public const ORANGE = '#f97316';

    public const ORANGE_50 = '#fff7ed';

    public const ORANGE_100 = '#ffedd5';

    public const ORANGE_200 = '#fed7aa';

    public const ORANGE_300 = '#fdba74';

    public const ORANGE_400 = '#fb923c';

    public const ORANGE_500 = '#f97316';

    public const ORANGE_600 = '#ea580c';

    public const ORANGE_700 = '#c2410c';

    public const ORANGE_800 = '#9a3412';

    public const ORANGE_900 = '#7c2d12';

    public const AMBER_50 = '#fffbeb';

    public const AMBER_100 = '#fef3c7';

    public const AMBER_200 = '#fde68a';

    public const AMBER_300 = '#fcd34d';

    public const AMBER_400 = '#fbbf24';

    public const AMBER_500 = '#f59e0b';

    public const AMBER_600 = '#d97706';

    public const AMBER_700 = '#b45309';

    public const AMBER_800 = '#92400e';

    public const AMBER_900 = '#78350f';

    public const YELLOW = 'yellow';

    public const BRIGHTYELLOW = 'bright-yellow';

    public const YELLOW_50 = '#fefce8';

    public const YELLOW_100 = '#fef9c3';

    public const YELLOW_200 = '#fef08a';

    public const YELLOW_300 = '#fde047';

    public const YELLOW_400 = '#facc15';

    public const YELLOW_500 = '#eab308';

    public const YELLOW_600 = '#ca8a04';

    public const YELLOW_700 = '#a16207';

    public const YELLOW_800 = '#854d0e';

    public const YELLOW_900 = '#713f12';

    public const LIME_50 = '#f7fee7';

    public const LIME_100 = '#ecfccb';

    public const LIME_200 = '#d9f99d';

    public const LIME_300 = '#bef264';

    public const LIME_400 = '#a3e635';

    public const LIME_500 = '#84cc16';

    public const LIME_600 = '#65a30d';

    public const LIME_700 = '#4d7c0f';

    public const LIME_800 = '#3f6212';

    public const LIME_900 = '#365314';

    public const GREEN = 'green';

    public const BRIGHTGREEN = 'bright-green';

    public const GREEN_50 = '#f0fdf4';

    public const GREEN_100 = '#dcfce7';

    public const GREEN_200 = '#bbf7d0';

    public const GREEN_300 = '#86efac';

    public const GREEN_400 = '#4ade80';

    public const GREEN_500 = '#22c55e';

    public const GREEN_600 = '#16a34a';

    public const GREEN_700 = '#15803d';

    public const GREEN_800 = '#166534';

    public const GREEN_900 = '#14532d';

    public const EMERALD_50 = '#ecfdf5';

    public const EMERALD_100 = '#d1fae5';

    public const EMERALD_200 = '#a7f3d0';

    public const EMERALD_300 = '#6ee7b7';

    public const EMERALD_400 = '#34d399';

    public const EMERALD_500 = '#10b981';

    public const EMERALD_600 = '#059669';

    public const EMERALD_700 = '#047857';

    public const EMERALD_800 = '#065f46';

    public const EMERALD_900 = '#064e3b';

    public const TEAL_50 = '#f0fdfa';

    public const TEAL_100 = '#ccfbf1';

    public const TEAL_200 = '#99f6e4';

    public const TEAL_300 = '#5eead4';

    public const TEAL_400 = '#2dd4bf';

    public const TEAL_500 = '#14b8a6';

    public const TEAL_600 = '#0d9488';

    public const TEAL_700 = '#0f766e';

    public const TEAL_800 = '#115e59';

    public const TEAL_900 = '#134e4a';

    public const CYAN = 'cyan';

    public const BRIGHTCYAN = 'bright-cyan';

    public const CYAN_50 = '#ecfeff';

    public const CYAN_100 = '#cffafe';

    public const CYAN_200 = '#a5f3fc';

    public const CYAN_300 = '#67e8f9';

    public const CYAN_400 = '#22d3ee';

    public const CYAN_500 = '#06b6d4';

    public const CYAN_600 = '#0891b2';

    public const CYAN_700 = '#0e7490';

    public const CYAN_800 = '#155e75';

    public const CYAN_900 = '#164e63';

    public const SKY_50 = '#f0f9ff';

    public const SKY_100 = '#e0f2fe';

    public const SKY_200 = '#bae6fd';

    public const SKY_300 = '#7dd3fc';

    public const SKY_400 = '#38bdf8';

    public const SKY_500 = '#0ea5e9';

    public const SKY_600 = '#0284c7';

    public const SKY_700 = '#0369a1';

    public const SKY_800 = '#075985';

    public const SKY_900 = '#0c4a6e';

    public const BLUE = 'blue';

    public const BRIGHTBLUE = 'bright-blue';

    public const BLUE_50 = '#eff6ff';

    public const BLUE_100 = '#dbeafe';

    public const BLUE_200 = '#bfdbfe';

    public const BLUE_300 = '#93c5fd';

    public const BLUE_400 = '#60a5fa';

    public const BLUE_500 = '#3b82f6';

    public const BLUE_600 = '#2563eb';

    public const BLUE_700 = '#1d4ed8';

    public const BLUE_800 = '#1e40af';

    public const BLUE_900 = '#1e3a8a';

    public const INDIGO_50 = '#eef2ff';

    public const INDIGO_100 = '#e0e7ff';

    public const INDIGO_200 = '#c7d2fe';

    public const INDIGO_300 = '#a5b4fc';

    public const INDIGO_400 = '#818cf8';

    public const INDIGO_500 = '#6366f1';

    public const INDIGO_600 = '#4f46e5';

    public const INDIGO_700 = '#4338ca';

    public const INDIGO_800 = '#3730a3';

    public const INDIGO_900 = '#312e81';

    public const VIOLET_50 = '#f5f3ff';

    public const VIOLET_100 = '#ede9fe';

    public const VIOLET_200 = '#ddd6fe';

    public const VIOLET_300 = '#c4b5fd';

    public const VIOLET_400 = '#a78bfa';

    public const VIOLET_500 = '#8b5cf6';

    public const VIOLET_600 = '#7c3aed';

    public const VIOLET_700 = '#6d28d9';

    public const VIOLET_800 = '#5b21b6';

    public const VIOLET_900 = '#4c1d95';

    public const PURPLE_50 = '#faf5ff';

    public const PURPLE_100 = '#f3e8ff';

    public const PURPLE_200 = '#e9d5ff';

    public const PURPLE_300 = '#d8b4fe';

    public const PURPLE_400 = '#c084fc';

    public const PURPLE_500 = '#a855f7';

    public const PURPLE_600 = '#9333ea';

    public const PURPLE_700 = '#7e22ce';

    public const PURPLE_800 = '#6b21a8';

    public const PURPLE_900 = '#581c87';

    public const FUCHSIA_50 = '#fdf4ff';

    public const FUCHSIA_100 = '#fae8ff';

    public const FUCHSIA_200 = '#f5d0fe';

    public const FUCHSIA_300 = '#f0abfc';

    public const FUCHSIA_400 = '#e879f9';

    public const FUCHSIA_500 = '#d946ef';

    public const FUCHSIA_600 = '#c026d3';

    public const FUCHSIA_700 = '#a21caf';

    public const FUCHSIA_800 = '#86198f';

    public const FUCHSIA_900 = '#701a75';

    public const PINK_50 = '#fdf2f8';

    public const PINK_100 = '#fce7f3';

    public const PINK_200 = '#fbcfe8';

    public const PINK_300 = '#f9a8d4';

    public const PINK_400 = '#f472b6';

    public const PINK_500 = '#ec4899';

    public const PINK_600 = '#db2777';

    public const PINK_700 = '#be185d';

    public const PINK_800 = '#9d174d';

    public const PINK_900 = '#831843';

    public const ROSE_50 = '#fff1f2';

    public const ROSE_100 = '#ffe4e6';

    public const ROSE_200 = '#fecdd3';

    public const ROSE_300 = '#fda4af';

    public const ROSE_400 = '#fb7185';

    public const ROSE_500 = '#f43f5e';

    public const ROSE_600 = '#e11d48';

    public const ROSE_700 = '#be123c';

    public const ROSE_800 = '#9f1239';

    public const ROSE_900 = '#881337';

    public const MAGENTA = 'magenta';

    public const BRIGHTMAGENTA = 'bright-magenta';
}
